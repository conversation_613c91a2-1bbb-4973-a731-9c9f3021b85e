/*
 * dq_svpwm.h
 *
 *  Created on: 2025年3月17日
 *      Author: Adam_
 */

#ifndef USER_DQ_SVPWM_H_
#define USER_DQ_SVPWM_H_

// 定义常量
#define C_SQRT_3 1.73205080756887729352F

#include "common.h"

// 定义结构体
typedef struct
{
    float ud;
    float uq;
    float sin_th;
    float cos_th;
    float pwma;
    float pwmb;
    float pwmc;
} dq_svpwm_t;

void DQ_SVPWM_F(dq_svpwm_t *handle);

/**
 * @brief 执行 SVPWM（空间矢量脉宽调制）算法
 * @param data 指向 dq_svpwm 结构体的指针，包含输入和输出数据
 */
#define SVPWM_CALC(t1, t2, pwm1, pwm2, pwm3) \
    {                                        \
        (pwm1) = (0.5f) * (-(t1) - (t2));    \
        (pwm2) = (pwm1) + (t1);              \
        (pwm3) = (pwm2) + (t2);              \
    }

#define DQ_SVPWM_MACRO__(handle)                                                \
    {                                                                           \
        float u_alpha, u_beta, v_a, v_b, v_c, x, y, z;                          \
                                                                                \
        u_alpha = handle.ud * handle.cos_th - handle.uq * handle.sin_th;        \
        u_beta = handle.ud * handle.sin_th + handle.uq * handle.cos_th;         \
                                                                                \
        /* 计算 v_a, v_b, v_c 用于扇区判断 */                                   \
        float u_beta_2 = (0.5f) * u_beta;                                       \
        float u_alpha_sqrt3_2 = ((0.5f) * C_SQRT_3) * u_alpha;                  \
                                                                                \
        v_a = u_beta;                                                           \
        v_b = -u_beta_2 + u_alpha_sqrt3_2;                                      \
        v_c = -u_beta_2 - u_alpha_sqrt3_2;                                      \
                                                                                \
        /* 计算 x, y, z */                                                      \
        x = u_beta;                                                             \
        y = u_beta_2 + u_alpha_sqrt3_2;                                         \
        z = u_beta_2 - u_alpha_sqrt3_2;                                         \
                                                                                \
        /* 判断扇区 */                                                          \
        uint16_t selector = (v_a >= 0) | ((v_b >= 0) << 1) | ((v_c >= 0) << 2); \
                                                                                \
        switch (selector)                                                       \
        {                                                                       \
        case 3: /* 扇区 I */                                                    \
            SVPWM_CALC(-z, x, handle.pwma, handle.pwmb, handle.pwmc);           \
            break;                                                              \
        case 1: /* 扇区 II */                                                   \
            SVPWM_CALC(z, y, handle.pwmb, handle.pwma, handle.pwmc);            \
            break;                                                              \
        case 5: /* 扇区 III */                                                  \
            SVPWM_CALC(x, -y, handle.pwmb, handle.pwmc, handle.pwma);           \
            break;                                                              \
        case 4: /* 扇区 IV */                                                   \
            SVPWM_CALC(-x, z, handle.pwmc, handle.pwmb, handle.pwma);           \
            break;                                                              \
        case 6: /* 扇区 V */                                                    \
            SVPWM_CALC(-y, -z, handle.pwmc, handle.pwma, handle.pwmb);          \
            break;                                                              \
        case 2: /* 扇区 VI */                                                   \
            SVPWM_CALC(y, -x, handle.pwma, handle.pwmc, handle.pwmb);           \
            break;                                                              \
        }                                                                       \
    }

#define DQ_SVPWM_MACRO(handle)                                                                             \
    {                                                                                                      \
        float u_alpha, u_beta, u_ref1, u_ref2, u_ref3;                                                     \
                                                                                                           \
        u_alpha = handle.ud * handle.cos_th - handle.uq * handle.sin_th;                                   \
        u_beta = handle.ud * handle.sin_th + handle.uq * handle.cos_th;                                    \
                                                                                                           \
        u_ref1 = u_beta;                                                                                   \
        u_ref2 = -0.5f * u_beta - (0.5f * C_SQRT_3) * u_alpha;                                             \
        u_ref3 = -0.5f * u_beta + (0.5f * C_SQRT_3) * u_alpha;                                             \
                                                                                                           \
        uint8_t selector = (u_ref1 > 0 ? 0x1 : 0x0) | (u_ref3 > 0 ? 0x2 : 0x0) | (u_ref2 > 0 ? 0x4 : 0x0); \
                                                                                                           \
        switch (selector)                                                                                  \
        {                                                                                                  \
        case 3:                                                                                            \
        {                                                                                                  \
            handle.pwmb = u_ref1;                                                                          \
            handle.pwma = u_ref3 + handle.pwmb;                                                            \
            handle.pwmc = (-handle.pwma) * (0.5f);                                                         \
            handle.pwma += handle.pwmc;                                                                    \
            handle.pwmb += handle.pwmc;                                                                    \
        }                                                                                                  \
        break;                                                                                             \
        case 1:                                                                                            \
        {                                                                                                  \
            handle.pwma = -u_ref2;                                                                         \
            handle.pwmb = -u_ref3 + handle.pwma;                                                           \
            handle.pwmc = (-handle.pwmb) * (0.5f);                                                         \
            handle.pwmb += handle.pwmc;                                                                    \
            handle.pwma += handle.pwmc;                                                                    \
        }                                                                                                  \
        break;                                                                                             \
        case 5:                                                                                            \
        {                                                                                                  \
            handle.pwmc = u_ref2;                                                                          \
            handle.pwmb = u_ref1 + handle.pwmc;                                                            \
            handle.pwma = (-handle.pwmb) * (0.5f);                                                         \
            handle.pwmb += handle.pwma;                                                                    \
            handle.pwmc += handle.pwma;                                                                    \
        }                                                                                                  \
        break;                                                                                             \
        case 4:                                                                                            \
        {                                                                                                  \
            handle.pwmb = -u_ref3;                                                                         \
            handle.pwmc = -u_ref1 + handle.pwmb;                                                           \
            handle.pwma = (-handle.pwmc) * (0.5f);                                                         \
            handle.pwmc += handle.pwma;                                                                    \
            handle.pwmb += handle.pwma;                                                                    \
        }                                                                                                  \
        break;                                                                                             \
        case 6:                                                                                            \
        {                                                                                                  \
            handle.pwma = u_ref3;                                                                          \
            handle.pwmc = u_ref2 + handle.pwma;                                                            \
            handle.pwmb = (-handle.pwmc) * (0.5f);                                                         \
            handle.pwmc += handle.pwmb;                                                                    \
            handle.pwma += handle.pwmb;                                                                    \
        }                                                                                                  \
        break;                                                                                             \
        case 2:                                                                                            \
        {                                                                                                  \
            handle.pwmc = -u_ref1;                                                                         \
            handle.pwma = -u_ref2 + handle.pwmc;                                                           \
            handle.pwmb = (-handle.pwma) * (0.5f);                                                         \
            handle.pwma += handle.pwmb;                                                                    \
            handle.pwmc += handle.pwmb;                                                                    \
        }                                                                                                  \
        break;                                                                                             \
        default:                                                                                           \
            break;                                                                                         \
        }                                                                                                  \
    }

#endif /* USER_DQ_SVPWM_H_ */
